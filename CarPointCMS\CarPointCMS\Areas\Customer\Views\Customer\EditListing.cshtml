@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerEditListingViewModel
@{
    ViewData["Title"] = "Edit Listing";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOther?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Edit Listing</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Edit Listing</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <form action="@Url.Action("EditListing", "Customer", new { id = Model.Listing.Id })" method="post" enctype="multipart/form-data">
                    @Html.AntiForgeryToken()

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Listing Name *</label>
                                <input type="text" name="listing_name" class="form-control" value="@Model.Listing.ListingName" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Listing Slug</label>
                                <input type="text" name="listing_slug" class="form-control" value="@Model.Listing.ListingSlug">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Listing Description *</label>
                                <textarea name="listing_description" class="form-control editor" cols="30" rows="10" required>@Model.Listing.ListingDescription</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Brand</label>
                                <select name="listing_brand_id" class="form-control select2">
                                    @foreach(var row in Model.ListingBrands)
                                    {
                                        @if(row.Id == Model.Listing.ListingBrandId)
                                        {
                                            <option value="@row.Id" selected>@row.ListingBrandName</option>
                                        }
                                        else
                                        {
                                            <option value="@row.Id">@row.ListingBrandName</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Location</label>
                                <select name="listing_location_id" class="form-control select2">
                                    @foreach(var row in Model.ListingLocations)
                                    {
                                        @if(row.Id == Model.Listing.ListingLocationId)
                                        {
                                            <option value="@row.Id" selected>@row.ListingLocationName</option>
                                        }
                                        else
                                        {
                                            <option value="@row.Id">@row.ListingLocationName</option>
                                        }
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Address</label>
                                <textarea name="listing_address" class="form-control h-70" cols="30" rows="10">@Model.Listing.ListingAddress</textarea>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Phone Number</label>
                                <input type="text" name="listing_phone" class="form-control" value="@Model.Listing.ListingPhone">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Email Address</label>
                                <input type="email" name="listing_email" class="form-control" value="@Model.Listing.ListingEmail">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Map iFrame Code</label>
                                <textarea name="listing_map" class="form-control h-70" cols="30" rows="10">@Model.Listing.ListingMap</textarea>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Website</label>
                                <input type="url" name="listing_website" class="form-control" value="@Model.Listing.ListingWebsite">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Existing Featured Photo</label>
                                <div>
                                    <img src="~/uploads/listing_featured_photos/@Model.Listing.ListingFeaturedPhoto" class="w-200" alt="">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Change Photo</label>
                                <input type="file" name="listing_featured_photo">
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Features</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Price *</label>
                                <input type="number" name="listing_price" class="form-control" step="0.01" value="@Model.Listing.ListingPrice" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Type</label>
                                <select name="listing_type" class="form-control">
                                    @if(Model.Listing.ListingType == "New Car")
                                    {
                                        <option value="New Car" selected>New Car</option>
                                        <option value="Used Car">Used Car</option>
                                    }
                                    else
                                    {
                                        <option value="New Car">New Car</option>
                                        <option value="Used Car" selected>Used Car</option>
                                    }
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Exterior Color</label>
                                <input type="text" name="listing_exterior_color" class="form-control" value="@Model.Listing.ListingExteriorColor">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Interior Color</label>
                                <input type="text" name="listing_interior_color" class="form-control" value="@Model.Listing.ListingInteriorColor">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Cylinder</label>
                                <input type="text" name="listing_cylinder" class="form-control" value="@Model.Listing.ListingCylinder">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Fuel Type</label>
                                <input type="text" name="listing_fuel_type" class="form-control" value="@Model.Listing.ListingFuelType">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Transmission</label>
                                <input type="text" name="listing_transmission" class="form-control" value="@Model.Listing.ListingTransmission">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Engine Capacity</label>
                                <input type="text" name="listing_engine_capacity" class="form-control" value="@Model.Listing.ListingEngineCapacity">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">VIN</label>
                                <input type="text" name="listing_vin" class="form-control" value="@Model.Listing.ListingVin">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Body</label>
                                <input type="text" name="listing_body" class="form-control" value="@Model.Listing.ListingBody">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Seat</label>
                                <input type="text" name="listing_seat" class="form-control" value="@Model.Listing.ListingSeat">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Wheel</label>
                                <input type="text" name="listing_wheel" class="form-control" value="@Model.Listing.ListingWheel">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Door</label>
                                <input type="text" name="listing_door" class="form-control" value="@Model.Listing.ListingDoor">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Mileage</label>
                                <input type="text" name="listing_mileage" class="form-control" value="@Model.Listing.ListingMileage">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Model Year</label>
                                <input type="text" name="listing_model_year" class="form-control" value="@Model.Listing.ListingModelYear">
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Opening Hours</h4>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Monday</label>
                                <input type="text" name="listing_oh_monday" class="form-control" value="@Model.Listing.ListingOhMonday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Tuesday</label>
                                <input type="text" name="listing_oh_tuesday" class="form-control" value="@Model.Listing.ListingOhTuesday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Wednesday</label>
                                <input type="text" name="listing_oh_wednesday" class="form-control" value="@Model.Listing.ListingOhWednesday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Thursday</label>
                                <input type="text" name="listing_oh_thursday" class="form-control" value="@Model.Listing.ListingOhThursday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Friday</label>
                                <input type="text" name="listing_oh_friday" class="form-control" value="@Model.Listing.ListingOhFriday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Saturday</label>
                                <input type="text" name="listing_oh_saturday" class="form-control" value="@Model.Listing.ListingOhSaturday">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Sunday</label>
                                <input type="text" name="listing_oh_sunday" class="form-control" value="@Model.Listing.ListingOhSunday">
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Existing Social Media</h4>
                    <div class="row">
                        @if(!Model.ListingSocialItems.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-danger">No Result Found</span>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        @foreach(var row in Model.ListingSocialItems)
                                        {
                                            <tr>
                                                <td>
                                                    @{
                                                        var iconCode = row.SocialIcon switch
                                                        {
                                                            "Facebook" => "fab fa-facebook-f",
                                                            "Twitter" => "fab fa-twitter",
                                                            "LinkedIn" => "fab fa-linkedin-in",
                                                            "YouTube" => "fab fa-youtube",
                                                            "Pinterest" => "fab fa-pinterest-p",
                                                            "GooglePlus" => "fab fa-google-plus-g",
                                                            "Instagram" => "fab fa-instagram",
                                                            _ => "fas fa-link"
                                                        };
                                                    }
                                                    <i class="@iconCode"></i>
                                                </td>
                                                <td>@row.SocialUrl</td>
                                                <td>
                                                    <a href="@Url.Action("DeleteSocialItem", "Customer", new { id = row.Id })" class="badge badge-danger fz-14 mt_5" onclick="return confirm('Are you sure?');">Delete</a>
                                                </td>
                                            </tr>
                                        }
                                    </table>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Add Social Media</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="social-media-container">
                                <div class="social-media-item row mb-3">
                                    <div class="col-md-4">
                                        <select name="social_icon[]" class="form-control">
                                            <option value="">Select Social Media</option>
                                            <option value="Facebook">Facebook</option>
                                            <option value="Twitter">Twitter</option>
                                            <option value="LinkedIn">LinkedIn</option>
                                            <option value="YouTube">YouTube</option>
                                            <option value="Pinterest">Pinterest</option>
                                            <option value="GooglePlus">Google Plus</option>
                                            <option value="Instagram">Instagram</option>
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <input type="url" name="social_url[]" class="form-control" placeholder="Social Media URL">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-social-media">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Amenities</h4>
                    <div class="row">
                        @{
                            var i = 0;
                        }
                        @foreach(var row in Model.Amenities)
                        {
                            i++;
                            <div class="col-md-4">
                                <div class="form-check mb_10">
                                    <input class="form-check-input amenity_check" name="amenity[]" type="checkbox" value="@row.Id" id="amenities@i" @(Model.ExistingAmenities.Contains(row.Id) ? "checked" : "")>
                                    <label class="form-check-label" for="amenities@i">
                                        @row.AmenityName
                                    </label>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Existing Photos</h4>
                    <div class="row">
                        @if(!Model.ListingPhotos.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-danger">No Photos Found</span>
                            </div>
                        }
                        else
                        {
                            @foreach(var photo in Model.ListingPhotos)
                            {
                                <div class="col-md-3 mb-3">
                                    <div class="card">
                                        <img src="~/uploads/listing_photos/@photo.PhotoName" class="card-img-top" alt="Listing Photo" style="height: 150px; object-fit: cover;">
                                        <div class="card-body p-2">
                                            <a href="@Url.Action("DeletePhoto", "Customer", new { id = photo.Id })" class="btn btn-danger btn-sm btn-block" onclick="return confirm('Are you sure?');">
                                                <i class="fas fa-trash"></i> Delete
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                    </div>

                    <h4 class="mt_30">Add Photos</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="photo-container">
                                <div class="photo-item row mb-3">
                                    <div class="col-md-10">
                                        <input type="file" name="listing_photos[]" class="form-control" accept="image/*">
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-photo">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">Existing Videos</h4>
                    <div class="row">
                        @if(!Model.ListingVideos.Any())
                        {
                            <div class="col-md-12">
                                <span class="text-danger">No Videos Found</span>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Video ID</th>
                                                <th>Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach(var video in Model.ListingVideos)
                                            {
                                                <tr>
                                                    <td>@video.VideoId</td>
                                                    <td>
                                                        <a href="@Url.Action("DeleteVideo", "Customer", new { id = video.Id })" class="badge badge-danger fz-14 mt_5" onclick="return confirm('Are you sure?');">Delete</a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    </div>

                    <h4 class="mt_30">Add Videos</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div id="video-container">
                                <div class="video-item row mb-3">
                                    <div class="col-md-10">
                                        <input type="text" name="video_id[]" class="form-control" placeholder="YouTube Video ID (e.g., dQw4w9WgXcQ)">
                                        <small class="form-text text-muted">Enter only the video ID from YouTube URL</small>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-success btn-sm add-video">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <h4 class="mt_30">SEO Section</h4>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Title</label>
                                <input type="text" name="seo_title" class="form-control" value="@Model.Listing.SeoTitle">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label for="">Meta Description</label>
                                <textarea name="seo_meta_description" class="form-control h-70" cols="30" rows="10">@Model.Listing.SeoMetaDescription</textarea>
                            </div>
                        </div>
                    </div>

                    @if(Model.AllowFeatured == "Yes")
                    {
                        <h4 class="mt_30">Featured Listing</h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <select name="is_featured" class="form-control">
                                        @if(Model.Listing.IsFeatured == true)
                                        {
                                            <option value="Yes" selected>Yes</option>
                                            <option value="No">No</option>
                                        }
                                        else
                                        {
                                            <option value="Yes">Yes</option>
                                            <option value="No" selected>No</option>
                                        }
                                    </select>
                                </div>
                            </div>
                        </div>
                    }

                    <button type="submit" class="btn btn-primary">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
<script>
$(document).ready(function() {
    // Social Media Add/Remove Functionality
    $(document).on('click', '.add-social-media', function() {
        var socialMediaHtml = `
            <div class="social-media-item row mb-3">
                <div class="col-md-4">
                    <select name="social_icon[]" class="form-control">
                        <option value="">Select Social Media</option>
                        <option value="Facebook">Facebook</option>
                        <option value="Twitter">Twitter</option>
                        <option value="LinkedIn">LinkedIn</option>
                        <option value="YouTube">YouTube</option>
                        <option value="Pinterest">Pinterest</option>
                        <option value="GooglePlus">Google Plus</option>
                        <option value="Instagram">Instagram</option>
                    </select>
                </div>
                <div class="col-md-6">
                    <input type="url" name="social_url[]" class="form-control" placeholder="Social Media URL">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-social-media">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#social-media-container').append(socialMediaHtml);
    });

    $(document).on('click', '.remove-social-media', function() {
        $(this).closest('.social-media-item').remove();
    });

    // Photo Add/Remove Functionality
    $(document).on('click', '.add-photo', function() {
        var photoHtml = `
            <div class="photo-item row mb-3">
                <div class="col-md-10">
                    <input type="file" name="listing_photos[]" class="form-control" accept="image/*">
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-photo">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#photo-container').append(photoHtml);
    });

    $(document).on('click', '.remove-photo', function() {
        $(this).closest('.photo-item').remove();
    });

    // Video Add/Remove Functionality
    $(document).on('click', '.add-video', function() {
        var videoHtml = `
            <div class="video-item row mb-3">
                <div class="col-md-10">
                    <input type="text" name="video_id[]" class="form-control" placeholder="YouTube Video ID (e.g., dQw4w9WgXcQ)">
                    <small class="form-text text-muted">Enter only the video ID from YouTube URL</small>
                </div>
                <div class="col-md-2">
                    <button type="button" class="btn btn-danger btn-sm remove-video">
                        <i class="fas fa-minus"></i>
                    </button>
                </div>
            </div>
        `;
        $('#video-container').append(videoHtml);
    });

    $(document).on('click', '.remove-video', function() {
        $(this).closest('.video-item').remove();
    });

    // Form Validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var errorMessage = '';

        // Check required fields
        if ($('input[name="listing_name"]').val().trim() === '') {
            isValid = false;
            errorMessage += 'Listing name is required.\n';
        }

        if ($('input[name="listing_price"]').val().trim() === '') {
            isValid = false;
            errorMessage += 'Listing price is required.\n';
        }

        if ($('textarea[name="listing_description"]').val().trim() === '') {
            isValid = false;
            errorMessage += 'Listing description is required.\n';
        }

        // Validate social media entries
        $('.social-media-item').each(function() {
            var icon = $(this).find('select[name="social_icon[]"]').val();
            var url = $(this).find('input[name="social_url[]"]').val();

            if ((icon && !url) || (!icon && url)) {
                isValid = false;
                errorMessage += 'Please complete both social media platform and URL, or leave both empty.\n';
                return false;
            }
        });

        // Validate video entries
        $('.video-item').each(function() {
            var videoId = $(this).find('input[name="video_id[]"]').val().trim();
            if (videoId && !/^[a-zA-Z0-9_-]{11}$/.test(videoId)) {
                isValid = false;
                errorMessage += 'Please enter a valid YouTube video ID (11 characters).\n';
                return false;
            }
        });

        if (!isValid) {
            alert(errorMessage);
            e.preventDefault();
            return false;
        }
    });

    // Initialize Select2 for better dropdowns
    if (typeof $.fn.select2 !== 'undefined') {
        $('.select2').select2({
            placeholder: 'Select an option',
            allowClear: true
        });
    }

    // Auto-generate slug from listing name
    $('input[name="listing_name"]').on('keyup', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
            .replace(/[^a-z0-9 -]/g, '') // Remove invalid chars
            .replace(/\s+/g, '-') // Replace spaces with -
            .replace(/-+/g, '-') // Replace multiple - with single -
            .trim('-'); // Trim - from start and end

        if ($('input[name="listing_slug"]').length) {
            $('input[name="listing_slug"]').val(slug);
        }
    });

    // Price formatting
    $('input[name="listing_price"]').on('keyup', function() {
        var value = $(this).val().replace(/[^0-9.]/g, '');
        $(this).val(value);
    });

    // Character counter for description
    $('textarea[name="listing_description"]').on('keyup', function() {
        var maxLength = 1000;
        var currentLength = $(this).val().length;
        var remaining = maxLength - currentLength;

        if (!$(this).next('.char-counter').length) {
            $(this).after('<small class="char-counter text-muted"></small>');
        }

        $(this).next('.char-counter').text(remaining + ' characters remaining');

        if (remaining < 0) {
            $(this).next('.char-counter').removeClass('text-muted').addClass('text-danger');
        } else {
            $(this).next('.char-counter').removeClass('text-danger').addClass('text-muted');
        }
    });
});
</script>
}
