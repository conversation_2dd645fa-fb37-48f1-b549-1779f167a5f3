* {
    margin: 0;
    padding: 0;
}

body {
    font-family: '<PERSON><PERSON>', sans-serif;
    color: #333;
    background: #fff;
    font-size: 16px;
}

.search-section h1 {
    font-family: 'Jost', sans-serif;
}

label {
    font-weight: 500!important;
}

.w_100 {width: 100px!important;}
.w_150 {width: 150px!important;}
.w_200 {width: 200px!important;}
.w_250 {width: 250px!important;}
.w_300 {width: 300px!important;}
.w_350 {width: 350px!important;}
.w_400 {width: 400px!important;}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #333;
}

.slider-video iframe {
    width: 100%;
    height: 600px;
}

.d_n {
    display: none!important;
}

.d_b {
    display: block!important;
}

a {
    -webkit-transition: all 0.4s ease!important;
    transition: all 0.4s ease!important;
    text-decoration: none;
}

a:hover {
    text-decoration: none;
}

.bg-lightblue {
    background: #f1f5f9;
}

.customer-dashboard-message {
    background: red;
    color: #fff;
    padding: 10px;
}

.limit {
    font-size: 14px;
    color: red;
}

.dashboard-box {
    padding: 20px;
    color: #fff;
    margin-bottom: 20px;
}

.dashboard-box-1 {
    background: #007bff;
}

.dashboard-box-2 {
    background: #28a745;
}

.dashboard-box-3 {
    background: #eaeaea;
    color: #333;
}

.dashboard-box-3 table,
.dashboard-box-3 table tr td {
    border: 1px solid #b1b1b1;
}

/* Modern Customer Dashboard Styles */
.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 30px;
    color: white;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.welcome-content h2 {
    margin: 0 0 10px 0;
    font-size: 2rem;
    font-weight: 600;
}

.welcome-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.welcome-actions {
    margin-top: 15px;
}

.btn-modern {
    border-radius: 25px;
    padding: 12px 30px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

/* Stats Cards */
.stats-card {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: none;
    position: relative;
    overflow: hidden;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.stats-card-primary::before {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.stats-card-warning::before {
    background: linear-gradient(90deg, #ffc107, #e0a800);
}

.stats-card-success::before {
    background: linear-gradient(90deg, #28a745, #1e7e34);
}

.stats-card-info::before {
    background: linear-gradient(90deg, #17a2b8, #117a8b);
}

.stats-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    font-size: 24px;
    color: white;
}

.stats-card-primary .stats-icon {
    background: linear-gradient(135deg, #007bff, #0056b3);
}

.stats-card-warning .stats-icon {
    background: linear-gradient(135deg, #ffc107, #e0a800);
}

.stats-card-success .stats-icon {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.stats-card-info .stats-icon {
    background: linear-gradient(135deg, #17a2b8, #117a8b);
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0 0 15px 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stats-action {
    margin-top: auto;
}

/* Package Card */
.package-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 30px;
}

.package-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 25px 30px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.package-title h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-weight: 600;
}

.package-name {
    color: #6c757d;
    font-size: 1.1rem;
    font-weight: 500;
}

.badge-lg {
    padding: 8px 16px;
    font-size: 0.9rem;
    border-radius: 20px;
}

.package-body {
    padding: 30px;
}

.package-details {
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #495057;
}

.detail-value {
    color: #2c3e50;
    font-weight: 600;
}

.package-progress {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.progress-label {
    font-weight: 500;
    color: #495057;
}

.progress-value {
    font-weight: 600;
    color: #2c3e50;
}

.progress-modern {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-modern .progress-bar {
    border-radius: 10px;
}

.package-footer {
    background: #f8f9fa;
    padding: 20px 30px;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

/* No Package Card */
.no-package-card {
    background: white;
    border-radius: 15px;
    padding: 60px 30px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
}

.no-package-content i {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 20px;
}

.no-package-content h4 {
    color: #2c3e50;
    margin-bottom: 15px;
}

.no-package-content p {
    color: #6c757d;
    margin-bottom: 30px;
    font-size: 1.1rem;
}

/* Quick Actions */
.quick-actions-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.quick-actions-card .card-title {
    margin-bottom: 25px;
    color: #2c3e50;
    font-weight: 600;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 25px 15px;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    background: #f8f9fa;
    color: #495057;
}

.quick-action-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    color: #495057;
}

.quick-action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 20px;
    color: white;
}

.quick-action-label {
    font-weight: 500;
    text-align: center;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-card {
        text-align: center;
        padding: 20px;
    }

    .welcome-content h2 {
        font-size: 1.5rem;
    }

    .stats-card {
        margin-bottom: 20px;
    }

    .package-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .package-footer {
        flex-direction: column;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}

/* Modern Customer Sidebar Styles */
.modern-sidebar {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    position: sticky;
    top: 20px;
}

/* Sidebar Profile Section */
.sidebar-profile {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 25px 20px;
    text-align: center;
    color: white;
}

.profile-avatar {
    margin-bottom: 15px;
}

.profile-avatar i {
    font-size: 3rem;
    opacity: 0.9;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.profile-image:hover {
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.05);
}

.profile-name {
    margin: 0 0 5px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.profile-role {
    margin: 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 0;
}

.nav-section {
    border-bottom: 1px solid #f8f9fa;
    padding: 0;
}

.nav-section:last-child {
    border-bottom: none;
}

.nav-section-logout {
    background: #f8f9fa;
}

.nav-section-title {
    padding: 15px 20px 10px 20px;
    margin: 0;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
    background: #f8f9fa;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin: 0;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    color: #495057;
    text-decoration: none;
    transition: all 0.3s ease;
    border: none;
    background: none;
    position: relative;
}

.nav-link:hover {
    background: #f8f9fa;
    color: #007bff;
    text-decoration: none;
    transform: translateX(5px);
}

.nav-link.active,
.nav-link:focus {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.1), transparent);
    color: #007bff;
    border-right: 3px solid #007bff;
}

.nav-link-logout {
    color: #dc3545;
}

.nav-link-logout:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.nav-icon {
    width: 20px;
    text-align: center;
    margin-right: 12px;
    font-size: 1rem;
}

.nav-text {
    font-weight: 500;
    font-size: 0.95rem;
}

/* Active state for current page */
.nav-link.current-page {
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.15), transparent);
    color: #007bff;
    border-right: 4px solid #007bff;
    font-weight: 600;
}

.nav-link.current-page .nav-icon {
    color: #007bff;
}

/* Hover effects */
.nav-link::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 0;
    background: linear-gradient(135deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.nav-link:hover::before {
    width: 4px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .modern-sidebar {
        margin-bottom: 20px;
        position: static;
    }

    .sidebar-profile {
        padding: 20px 15px;
    }

    .profile-avatar i {
        font-size: 2.5rem;
    }

    .nav-link {
        padding: 12px 15px;
    }

    .nav-section-title {
        padding: 12px 15px 8px 15px;
        font-size: 0.75rem;
    }
}

/* Compact sidebar for smaller screens */
@media (max-width: 576px) {
    .nav-text {
        font-size: 0.9rem;
    }

    .nav-icon {
        margin-right: 10px;
    }
}

/* Click animation */
.nav-link.clicked {
    transform: scale(0.98);
    transition: transform 0.1s ease;
}

/* Enhanced hover states */
.nav-link:hover .nav-icon {
    transform: scale(1.1);
    transition: transform 0.2s ease;
}

/* Smooth transitions for all elements */
.nav-link,
.nav-icon,
.nav-text {
    transition: all 0.3s ease;
}

/* Badge for notifications (future enhancement) */
.nav-badge {
    position: absolute;
    top: 8px;
    right: 15px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}

/* Sidebar footer */
.sidebar-footer {
    padding: 20px;
    text-align: center;
    border-top: 1px solid #f8f9fa;
    background: #f8f9fa;
}

.sidebar-footer small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Loading state for nav links */
.nav-link.loading {
    opacity: 0.6;
    pointer-events: none;
}

.nav-link.loading .nav-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.contact-page-map iframe {
    width: 100%;
    height: 500px;
    margin-bottom: 50px;
}

.w-400 {
    width: 400px!important;
}

.w-300 {
    width: 300px!important;
}

.w-200 {
    width: 200px!important;
}

.w-100 {
    width: 100px!important;
}

.w-150 {
    width: 150px!important;
}

.w-100-p {
    width: 100%!important;
}

.h-100 {
    height: 100px!important;
}

.h-70 {
    height: 70px!important;
}

.h-200 {
    height: 200px!important;
}

.h-300 {
    height: 300px!important;
}

.btn-arf {
    background: #e00445!important;
    border: 0;
}

.btn-arf:hover {
    background: #333!important;
}

.existing-video iframe {
    width: 100%;
    height: 170px;
}

.psp_pagination .page-link {
    color: #3dab3b;
    outline: none;
    width: 35px;
    text-align: center;
    height: 35px;
    font-size: 12px;
    padding: 0;
    line-height: 35px;
    font-weight: 700;
    border-top: none;
    border-bottom: none;
    border-color: #eee;
    transition: all linear .3s;
    -webkit-transition: all linear .3s ease;
    -moz-transition: all linear .3s ease;
    -ms-transition: all linear .3s ease;
    -o-transition: all linear .3s ease;
    box-shadow: rgb(0 0 0 / 10%) 0px 1px 4px;
    margin: 0px 5px;
    border-radius: 3px !important;
    background: #fff;
}

.psp_pagination .page-link:focus {
    box-shadow: none;
}

.psp_pagination .page-item.active .page-link,
.psp_pagination .page-item .page-link:hover {
    z-index: 3;
    color: #fff;
    background-color: #3dab3b;
    border-color: #ddd;
}

.top {
    height: 36px;
}

.top ul.top-left {
    width: 100%;
}

.top ul.top-left li {
    list-style-type: none;
    float: left;
    padding-top: 6px;
    margin-right: 30px;
}

.top ul.top-left li,
.top ul.top-left li a {
    color: #fff;
    font-size: 14px;
}

.top ul.top-right {
    width: 100%;
}

.top ul.top-right li {
    list-style-type: none;
    float: right;
}

.top ul.top-right li,
.top ul.top-right li a {
    color: #fff;
    font-size: 14px;
}

.top ul.top-right select {
    background: transparent!important;
    color: #fff!important;
    border: 0!important;
    font-size: 14px;
    margin-top: 1px;
    padding-left: 10px;
    padding-right: 0;
}

.top ul.top-right select option {
    color: #000!important;
}

@media only screen and (max-width: 991px) {
    .top {
        height: auto;
    }
    .top ul.top-left li {
        float: none;
    }
}

@media only screen and (max-width: 767px) {
    .top {
        height: auto;
    }
    .top ul.top-left {
        margin-top: 10px;
    }
    .top ul.top-left,
    .top ul.top-right {
        text-align: center;
    }
    .top ul.top-left li,
    .top ul.top-right li {
        float: none;
        display: inline-block;
    }
}

.search-section {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    position: relative;
    padding-top: 165px;
    padding-bottom: 165px;
}
.search-section .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0d263b;
    opacity: 0.7;
}
.search-section h1 {
    color: #fff;
    font-weight: 700;
    font-size: 50px;
    text-align: center;
}
.search-section p {
    color: #fff;
    font-weight: 400;
    font-size: 20px;
    text-align: center;
}
.search-section .box {
    margin-top: 30px;
}
.search-section .input-box {
    background: rgb(245, 245, 245, 0.3);
    padding: 10px;
    border-radius: 6px;
}
.search-section .input-box input {
    width: 20%;
    height: 40px;
    font-size: 16px!important;
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}
.search-section .input-box .select2-container {
    width: 20%!important;
    height: 40px;
    font-size: 16px!important;
}
.search-section .input-group-append {
    width: 20%!important;
    height: 40px;
    font-size: 16px!important;
}
.search-section .input-group-append button {
    width: 100%!important;
    background: #e00445;
    border: 0;
    color: #fff;
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}
.select2-container--bootstrap .select2-selection {
    font-size: 16px!important;
}
.select2-container--bootstrap .select2-selection--single {
    height: 40px;
    line-height: 26px;
}
@media only screen and (max-width: 767px) {
    .search-section {
        padding-top: 100px!important;
        padding-bottom: 50px!important;
    }
    .search-section .input-group {
        display: block;
    }
    .search-section .input-box input {
        width: 100%;
        border-top-right-radius: 6px!important;
        border-bottom-right-radius: 6px!important;
        margin-bottom: 5px;
    }
    .search-section .input-box .select2-container {
        width: 100%!important;
    }
    .search-section .input-group-append {
        width: 100%!important;
    }
    .select2-container--bootstrap .select2-selection--single {
        border-radius: 6px!important;
        margin-bottom: 5px!important;
    }
    .search-section .input-group-append button {
        border-top-left-radius: 6px!important;
        border-bottom-left-radius: 6px!important;
    }
}


/* Main Nav */
.main-nav {
    position: relative;
    padding-top: 0;
    padding-bottom: 0;
    background: #fff;
}
.main-nav .navbar {
    padding-left: 0;
    padding-right: 0;
    padding-top: 0;
    padding-bottom: 0;
}
.main-nav nav .navbar-nav .nav-item {
    padding-top: 28px;
    padding-bottom: 28px;
}
.main-nav nav .navbar-nav .nav-item .dropdown-menu {
    top: 80px!important;
    background: #ffffff!important;
    padding: 0!important;
    border: 2px solid #f5f5f5!important;
    border-radius: 0!important;
}
.main-nav nav .navbar-nav .nav-item a {
    color: #333;
    font-weight: 600;
    text-transform: none!important;
}
.main-nav nav .navbar-nav .nav-item .dropdown-menu li a {
    color: #444;
    border-bottom: 1px solid #dbdbdb;
}
.mobile-nav.mean-container .mean-nav ul li a.active,
.main-nav nav .navbar-nav .nav-item a:hover,
.main-nav nav .navbar-nav .nav-item a:focus,
.main-nav nav .navbar-nav .nav-item a.active,
.main-nav nav .navbar-nav .nav-item:hover a,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li a:hover,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li a:focus,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li a.active,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:hover,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a:focus,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li .dropdown-menu li a.active,
.main-nav nav .navbar-nav .nav-item .dropdown-menu li:hover a {
    color: #e00445;
}
.main-nav nav .navbar-nav .nav-item .dropdown-menu li a:hover {
    color: #e00445!important;
}
.main-nav img {
    height: 70px;
}


/* Sticky Nav */
.sticky {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    z-index: 999;
}


/* Sidepanel */
.sidepanel {
    height: 250px;
    width: 0;
    position: fixed;
    z-index: 1;
    top: 0;
    left: 0;
    background-color: #111;
    overflow-x: hidden;
    padding-top: 60px;
    transition: 0.5s;
}
.sidepanel a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}
.sidepanel a:hover {
    color: #f1f1f1;
}
.sidepanel .closebtn {
    position: absolute;
    top: 0;
    right: 25px;
    font-size: 36px;
    margin-left: 50px;
}
.openbtn {
    font-size: 20px;
    cursor: pointer;
    background-color: #111;
    color: white;
    padding: 10px 15px;
    border: none;
}
.openbtn:hover {
    background-color: #444;
}


/* =============================================== */
/* ================= Mobile Menu ================= */
/* =============================================== */
.mobile-nav img {
    height: 40px;
}
.mean-container .mean-bar {
    background-color: #fff;
    height: 60px;
}
.mean-container .logo {
    top: 5px;
}
.mean-container img {
    height: 50px;
}
.mean-container a.meanmenu-reveal span {
    background: #e00445;
}
.mean-container a.meanmenu-reveal {
    color: #e00445;
}


/* Popular Brand */
.popular-brand {
    padding-top: 50px;
    overflow: hidden;
}
.popular-brand .heading {
    text-align: center;
    margin-bottom: 30px;
}
.popular-brand .heading h2 {
    font-size: 30px;
    font-weight: 700;
    color: #333;
    margin-top: 0;
}
.popular-brand .heading h3 {
    font-size: 18px;
    font-weight: 400;
    color: #8c8c8c;
}
.popular-brand .popular-brand-item {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center center;
    padding: 10px;
    color: #fff;
    text-align: center;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    position: relative;
    margin-bottom: 25px;
    height: 200px;
    border-radius: 8px;
    overflow: hidden;
}
.popular-brand .popular-brand-item:hover {
    margin-top: -10px;
}
.popular-brand .popular-brand-item .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0) 0%, rgba(38, 38, 38, 0.49) 99%);
    filter: progid: DXImageTransform.Microsoft.gradient( startColorstr='#a6000000', endColorstr='#00000000', GradientType=0);
    border-radius: 8px;
}
.popular-brand .popular-brand-item .icon {
    font-size: 30px;
    margin-bottom: 20px;
    color: #fff;
    position: relative;
}
.popular-brand .popular-brand-item .text {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: auto;
    opacity: 1;
}
.popular-brand .popular-brand-item .text h4 {
    color: #fff;
    font-weight: 700;
    font-size: 22px;
    margin-bottom: 5px;
}
.popular-brand .popular-brand-item .text p {
    color: #fff;
    font-size: 15px;
    margin-bottom: 10px;
}
.popular-brand .popular-brand-item a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

@media only screen and (max-width: 1199px) {
    .popular-brand .popular-brand-item {
        height: 170px;
    }
}

@media only screen and (max-width: 991px) {
    .popular-brand .popular-brand-item {
        height: 200px;
    }
}

@media only screen and (max-width: 767px) {
    .popular-brand .popular-brand-item {
        height: 150px;
    }
}

@media only screen and (max-width: 575px) {
    .popular-brand .popular-brand-item {
        height: 300px;
    }
}

@media only screen and (max-width: 460px) {
    .popular-brand .popular-brand-item {
        height: 260px;
    }
}


/* Popular City */
.popular-city {
    padding-top: 50px;
    padding-bottom: 20px;
    overflow: hidden;
}
.popular-city .heading {
    text-align: center;
    margin-bottom: 30px;
}
.popular-city .heading h2 {
    font-size: 30px;
    font-weight: 700;
    color: #333;
    margin-top: 0;
}
.popular-city .heading h3 {
    font-size: 18px;
    font-weight: 400;
    color: #8c8c8c;
}
.popular-city .popular-city-item {
    color: #fff;
    text-align: center;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    position: relative;
    margin-bottom: 25px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 0px 10px 3px #e2e2e2;
}
.popular-city .popular-city-item .icon {
    font-size: 30px;
    margin-bottom: 20px;
    color: #fff;
    position: relative;
}
.popular-city .popular-city-item .photo {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.popular-city .popular-city-item .photo img {
    width: 100%;
    height: 170px;
    object-fit: cover;
    object-position: center;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    transition-duration: .5s;
}
.popular-city .popular-city-item:hover .photo img {
    transform: scale(1.2) rotate(5deg);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}
.popular-city .popular-city-item .text {
    padding-top: 10px;
    padding-bottom: 10px;
}
.popular-city .popular-city-item .text h4 {
    color: #333;
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 5px;
}
.popular-city .popular-city-item:hover h4 {
    color: #385FDD;
}
.popular-city .popular-city-item .text p {
    color: #8c8c8c;
    font-size: 14px;
    margin-bottom: 10px;
}
.popular-city .popular-city-item a {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}
.popular-city-carousel .owl-dots {
    text-align: center;
    margin-top: 5px;
}
.popular-city-carousel .owl-dots .owl-dot {
    width: 24px;
    height: 24px;
    background: #c7c7c7;
    border: 7px solid #c7c7c7;
    border-radius: 50%;
    display: inline-block;
    margin: 0 5px;
}
.popular-city-carousel .owl-dots .owl-dot.active {
    width: 24px;
    height: 24px;
    background: #000;
    border: 7px solid #c7c7c7;
}
@media only screen and (max-width: 1199px) {
    .popular-city .popular-city-item {
        height: 170px;
    }
}

@media only screen and (max-width: 991px) {
    .popular-city .popular-city-item {
        height: 260px;
    }
}

@media only screen and (max-width: 767px) {
    .popular-city .popular-city-item {
        height: 320px;
    }
}

@media only screen and (max-width: 575px) {
    .popular-city-carousel .owl-nav .owl-prev {
        left: -10px;
    }
    .popular-city-carousel .owl-nav .owl-next {
        right: -10px;
    }
}


/* Popular Listing */
.listing {
    padding-top: 50px;
    padding-bottom: 20px;
    overflow: hidden;
}

.listing .heading {
    text-align: center;
    margin-bottom: 30px;
}

.listing .heading h2 {
    font-size: 30px;
    font-weight: 700;
    color: #333;
    margin-top: 0;
}

.listing .heading h3 {
    font-size: 18px;
    font-weight: 400;
    color: #8c8c8c;
}

.listing .listing-item {
    margin-bottom: 25px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px 3px #e2e2e2;
}

.listing .listing-item .photo {
    position: relative;
    overflow: hidden;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.listing .listing-item .photo img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    transition-duration: .5s;
}

.listing .listing-item:hover .photo img {
    transform: scale(1.2) rotate(5deg);
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.listing .listing-item .photo .brand {
    position: absolute;
    bottom: 10px;
    left: 10px;
}

.listing .listing-item .photo .brand a {
    background: #e00445;
    color: #fff;
    padding: 2px 8px;
    font-size: 14px;
    border-radius: 6px;
}

.listing .listing-item .photo .wishlist {
    position: absolute;
    bottom: 10px;
    right: 10px;
}

.listing .listing-item .photo .wishlist i {
    color: #fff;
    font-size: 30px;
    font-weight: 400;
}

.listing .listing-item .photo .featured-text {
    position: absolute;
    top: 20px;
    right: -30px;
    background: #3dab3b;
    color: #fff;
    padding-left: 30px;
    padding-right: 30px;
    -webkit-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
}

.listing .listing-item .text {
    background: #fff;
    padding: 20px;
    border-bottom-left-radius: 8px;
    border-bottom-right-radius: 8px;
}

.listing .listing-item .text .type-price {
    margin-bottom: 10px;
    border-bottom: 1px dashed #c7c7c7;
    padding-bottom: 10px;
}

.listing .listing-item .text .type-price .type {
    display: inline-block;
    font-size: 13px;
    font-weight: 700;
    vertical-align: middle;
    width: 40%;
}

.listing .listing-item .text .type-price .type .inner-new {
    background: #d3dcfb;
    color: #6476d3;
    padding: 2px 8px;
    border: 1px solid #8f9ff5;
    display: inline;
    border-radius: 6px;
}

.listing .listing-item .text .type-price .type .inner-used {
    background: #f6e6ca;
    color: #b4911c;
    padding: 2px 8px;
    border: 1px solid #c4a84b;
    display: inline;
    border-radius: 6px;
}

.listing .listing-item .text .type-price .price {
    display: inline-block;
    color: #202020;
    font-weight: 700;
    font-size: 22px;
    vertical-align: middle;
    text-align: right;
    width: calc(60% - 6px);
}

.listing .listing-item .text .type-price .price .per-night {
    color: #828282;
    font-size: 14px;
}

.listing .listing-item .text h3 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
}

.listing .listing-item .text h3 a {
    color: #333;
}

.listing .listing-item .text h3 a:hover {
    color: #e00445;
}

.listing .listing-item .text .location {
    font-size: 16px;
    color: #e00445;
}

.listing .listing-item .text .location a {
    color: #e00445;
}

.my-review i {
    color: #fbc715;
    font-size: 14px;
}

.listing .listing-item .text .review {
    margin-top: 5px;
}

.listing .listing-item .text .review i {
    color: #fbc715;
    font-size: 14px;
}

.listing .listing-item .bed-bath-size {
    overflow: hidden;
    margin-top: 20px;
    border-top: 1px dashed #c7c7c7;
    padding-top: 10px;
}

.listing .listing-item .bed-bath-size .item {
    float: left;
    width: 33.33%;
}

.listing .listing-item .bed-bath-size .item .icon {
    text-align: center;
}

.listing .listing-item .bed-bath-size .item .icon i {
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50%;
    color: #236884;
}

.listing .listing-item .bed-bath-size .item .text {
    text-align: center;
    padding: 0;
    font-size: 14px;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(1) .icon {
    text-align: left;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(2) .icon {
    text-align: center;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(3) .icon {
    text-align: right;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(1) .text {
    text-align: left;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(2) .text {
    text-align: center;
}

.listing .listing-item .bed-bath-size .item:nth-of-type(3) .text {
    text-align: right;
}

.listing .owl-dots {
    text-align: center;
    margin-top: 5px;
}

.listing .owl-dots .owl-dot {
    width: 24px;
    height: 24px;
    background: #c7c7c7;
    border: 7px solid #c7c7c7;
    border-radius: 50%;
    display: inline-block;
    margin: 0 5px;
}

.listing .owl-dots .owl-dot.active {
    width: 24px;
    height: 24px;
    background: #000;
    border: 7px solid #c7c7c7;
}

@media only screen and (max-width: 1199px) {
    .listing .listing-item .photo img {
        height: 185px;
    }
}

@media only screen and (max-width: 991px) {
    .listing .listing-item .photo img {
        height: 215px;
    }
}

@media only screen and (max-width: 767px) {
    .listing .listing-item .photo img {
        height: 320px;
    }
}

@media only screen and (max-width: 575px) {
    .listing .listing-item .photo img {
        height: 340px;
    }
    .listing .owl-nav .owl-prev {
        left: -10px;
    }
    .listing .owl-nav .owl-next {
        right: -10px;
    }
}

@media only screen and (max-width: 460px) {
    .listing .listing-item .photo img {
        height: 270px;
    }
}


/* Pricing */

.pricing .card {
    border: none;
    border-radius: 6px;
    transition: all 0.2s;
    background: #f3f3f3;
}

.pricing hr {
    margin: 1.5rem 0;
}

.pricing .card-title {
    margin: 0.5rem 0;
    font-size: 20px;
    letter-spacing: .1rem;
    font-weight: bold;
}

.pricing .card-price {
    font-size: 32px;
    margin: 0;
    font-weight: 700;
}

.pricing .card-price .period {
    font-size: 16px;
}

.pricing ul li {
    margin-bottom: 16px;
}

.pricing .text-muted {
    opacity: 0.7;
}

.pricing .btn {
    background: #e00445;
    font-size: 20px;
    border-radius: 6px;
    font-weight: 700;
    padding: 10px;
    border: 0;
}

.pricing .btn:hover {
    background: #333;
}


/* Hover Effects on Card */

@media (min-width: 991px) {
    .pricing .card:hover {
        margin-top: -.25rem;
        margin-bottom: .25rem;
        box-shadow: 0 0.5rem 1rem 0 rgba(0, 0, 0, 0.3);
    }
    .pricing .card:hover .btn {
        opacity: 1;
    }
}


/* Scroll to Top */

.scroll-top {
    cursor: pointer;
    text-align: center;
    font-size: 22px;
    position: fixed;
    width: 50px;
    height: 50px;
    line-height: 46px;
    bottom: 20px;
    right: 20px;
    background: #e00445;
    color: #fff;
    opacity: 0.7;
    z-index: 99;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}

.scroll-top:hover {
    opacity: 1;
    color: #fff;
}


/* Footer */
.footer-area {
    margin-top: 50px;
    padding-top: 30px;
    padding-bottom: 60px;
    position: relative;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    background-image: url('../../images/footer_bg.jpg');
}

.footer-area:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0d263b;
    opacity: 0.95;
}

.footer-item {
    margin-top: 30px;
}

.footer-item h2 {
    font-size: 22px;
    font-weight: 600;
    margin: 0;
    margin-bottom: 30px;
    color: #fff!important;
}

.footer-item p {
    line-height: 1.7;
    margin: 0;
}

.footer-item ul li {
    margin-bottom: 5px;
    list-style-type: none;
}

.footer-item ul li a {
    font-size: 15px;
    font-weight: 400;
    color: #fff;
    transition: all 0.4s;
    padding-left: 5px;
}

.footer-item ul.fmain li {
    position: relative;
    padding-left: 10px;
}

.footer-item ul.fmain li:before {
    content: '\f105';
    font-family: "Font Awesome 5 Free";
    color: #fff;
    font-weight: 600;
    position: absolute;
    left: 0;
    top: 0;
}

.footer-item ul li a:hover {
    color: #e4e4e4;
}

.footer-social-link {
    margin-top: 30px;
}

.footer-social-link h2 {
    font-size: 26px;
    font-weight: 600;
    text-transform: capitalize;
    margin: 0;
    margin-bottom: 20px;
}

.footer-social-link ul li {
    display: inline-block;
    margin-right: 5px;
}

.footer-social-link ul li a {
    text-align: center;
    display: block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    background: #e00445;
    border: 1px solid #e00445;
    color: #fff;
    transition: all 0.4s;
    padding-left: 0;
    border-radius: 3px;
}

.footer-social-link ul li a:hover {
    background-color: #fff;
    color: #385FDD;
    border-color: #fff;
}

.footer-contact ul li {
    position: relative;
    padding-left: 26px;
    font-size: 15px;
    margin-bottom: 8px;
    color: #fff;
}

.footer-contact ul li:before {
    font-family: "Font Awesome 5 Free";
    position: absolute;
    left: 0;
    top: 0;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
}

.footer-contact ul li:nth-child(1):before {
    content: '\f3c5';
}

.footer-contact ul li:nth-child(2):before {
    content: '\f0e0';
}

.footer-contact ul li:nth-child(3):before {
    content: '\f2a0';
}

.copyright {
    text-align: center;
    color: #fff;
    border-top: 1px solid #a8a8a8;
    margin-top: 20px;
    padding-top: 20px;
}

.copyright p {
    margin: 0;
}


/* Page Banner */

.page-banner {
    background: #e00445;
    background-position: center center!important;
    background-size: cover!important;
    background-repeat: no-repeat!important;
    position: relative;
    padding-top: 30px;
    padding-bottom: 30px;
}

.page-banner .page-banner-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #0d263b;
    opacity: 0.5;
}

.page-banner h1 {
    color: #fff;
    font-size: 34px;
    font-weight: 700;
    padding-top: 30px;
    margin: 0;
    text-align: center;
    position: relative;
}

.page-banner nav {
    padding-bottom: 20px!important;
    position: relative;
}

.page-banner .breadcrumb {
    background-color: transparent;
    text-align: center;
    margin-bottom: 0;
}

.page-banner .breadcrumb-item {
    color: #e9e9e9;
    font-weight: 600;
    font-size: 16px;
}

.page-banner .breadcrumb-item a {
    color: #fff;
}

.page-banner .breadcrumb-item::before {
    color: #fff!important;
}

@media only screen and (max-width: 991px) {
    .page-banner {
        padding-top: 70px!important;
    }
}


/* Page Content */

.page-content {
    padding-top: 60px;
    padding-bottom: 0;
    overflow: hidden;
}


/* Blog Page */

.blog-item {
    margin-bottom: 50px;
}

.blog-item .text h2 {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
}

.blog-item .text h2 a {
    color: #333;
}

.blog-item .featured-photo {
    margin-bottom: 15px;
}

.blog-item .featured-photo img {
    width: 100%;
    height: 230px;
    object-fit: cover;
    border-radius: 6px;
}

.blog-item-single .featured-photo {
    margin-bottom: 15px;
}

.blog-item-single .featured-photo img {
    width: 100%;
    height: auto;
    border-radius: 6px;
}

.blog-item-single .text h2 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #333;
}

.blog-item-single .text h2 a {
    color: #333;
}


/* Sidebar */

.sidebar .widget {
    padding: 20px;
    background: #f1f5f9;
    margin-bottom: 30px;
}

.sidebar .widget h3 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 20px;
}

.sidebar .widget .type-1 ul li {
    list-style-type: none;
    padding-left: 20px;
    position: relative;
    margin-bottom: 10px;
}

.sidebar .widget .type-1 ul li:before {
    content: '\f105';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    left: 0;
    top: 0;
    color: #e00445;
    font-weight: 600;
}

.sidebar .widget .type-1 ul li a {
    color: #333;
}

.sidebar .widget .type-1 ul li a:hover {
    color: #e00445;
}

.sidebar .widget .type-2 img {
    width: 70px;
    height: auto;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
}

.sidebar .widget .type-2 ul li {
    list-style-type: none;
    position: relative;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #cacaca;
}

.sidebar .widget .type-2 ul li a {
    width: calc(100% - 86px);
    color: #333;
    display: inline-block;
    vertical-align: middle;
}

.sidebar .widget .type-2 ul li a:hover {
    color: #e00445;
}

.sidebar .widget .search .input-group-append {
    width: 36px;
}

.sidebar .widget .search button {
    background: #e00445;
    border: 0;
    width: 36px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.sidebar .widget .search button i {
    color: #fff;
}

.sidebar .widget .type-3 button {
    border: 0;
    background: #e00445;
    border-radius: 50px;
    padding: 10px 30px;
    font-weight: 600;
    text-transform: uppercase;
}

.sidebar .widget .type-3 button:hover {
    background: #333;
}

.sidebar .widget .project-detail .item {
    margin-bottom: 25px;
}

.sidebar .widget .project-detail .item h4 {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 5px;
}

.sidebar .widget .project-detail .item p {
    color: #555;
    font-size: 15px;
}


/* Portfolio Carousel */

.portfolio-carousel {
    overflow: hidden;
    margin-top: 30px;
}

.portfolio-photo-item {
    background-size: cover;
    background-position: top center;
    background-repeat: no-repeat;
    height: 370px;
    position: relative;
}

.portfolio-carousel .owl-nav .owl-prev,
.portfolio-carousel .owl-nav .owl-next {
    text-align: center;
    font-size: 18px;
    position: absolute;
    top: 50%;
    width: 40px;
    height: 50px;
    line-height: 48px;
    background: #e00445;
    color: #fff;
    margin-top: -25px;
    transition: all 0.3s;
    -webkit-transition: all 0.3s;
}

.portfolio-carousel .owl-nav .owl-prev {
    left: 0;
}

.portfolio-carousel .owl-nav .owl-next {
    right: 0;
}

.portfolio-carousel .owl-nav .owl-prev:hover,
.portfolio-carousel .owl-nav .owl-next:hover {
    color: #fff;
    background: #313131;
}

.single-portfolio .iframe-container {
    overflow: hidden;
    clear: both;
}

.single-portfolio iframe {
    width: 100%;
    height: 400px;
}

.portfolio-carousel .p-item {
    position: relative;
}

.portfolio-carousel .p-item .p-item-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    background: #333;
    z-index: 9;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

.portfolio-carousel .p-item:hover .p-item-bg {
    opacity: 0.6;
}

.portfolio-carousel .p-item .plus-icon {
    visibility: hidden;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    line-height: 100%;
    text-align: center;
    z-index: 99;
    font-size: 30px;
    color: #fff;
}

.portfolio-carousel .p-item:hover .plus-icon {
    visibility: visible;
    display: flex;
    align-items: center;
    justify-content: center;
}


/* Comment */

.comment h2 {
    font-size: 28px;
    font-weight: 700;
}

.comment button {
    border: 0;
    background: #e00445;
    border-radius: 8px;
    padding: 10px 30px;
    font-weight: 600;
    text-transform: none;
}

.comment button:hover {
    background: #333;
}

.comment-item {
    margin-bottom: 20px;
    display: flex;
}

.comment-item .photo {
    width: 70px;
    margin-right: 15px;
}

.comment-item .photo img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 2px solid #ddd;
}

.comment-item .text h4 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 0;
}

.comment-item .text .date {
    font-size: 16px;
    color: #848484;
    margin-bottom: 10px;
}

.comment-item .text .des {
    font-size: 14px;
}


/* Career Detail */

.career-detail .item {
    margin-bottom: 30px;
}

.career-detail .item h3 {
    font-size: 22px;
    font-weight: 700;
}

.career-detail .item p {
    font-size: 16px;
}

.career-detail .item ul li {
    list-style-type: none;
    position: relative;
    padding-left: 25px;
}

.career-detail .item ul li:before {
    content: '\f105';
    font-family: 'FontAwesome';
    position: absolute;
    left: 10px;
    top: 0;
}


/* FAQ */

.faq {
    background: #fff;
}

.faq .panel-body ol li,
.faq .panel-body ul li {
    list-style-position: inside;
}

.faq .panel {
    box-shadow: none!important;
    border-radius: 0!important;
    margin-bottom: 10px;
}

.faq .panel-default>.panel-heading {
    background: #eee;
    color: #333;
    padding: 0;
    border-radius: 0;
    border: 1px solid #eee;
    position: relative;
}

.faq .panel-group .panel-heading a:after {
    content: '\f068';
    font-family: 'Font Awesome 5 Free';
    position: absolute;
    font-weight: 600;
    right: 20px;
    top: 10px;
    font-size: 14px;
}

.faq .panel-group .panel-heading a.collapsed:after {
    content: '\f067';
    font-family: 'Font Awesome 5 Free';
    font-size: 14px;
    font-weight: 600;
}

.faq h4.panel-title {
    margin-bottom: 0;
    border: 1px solid #ddd;
}

.faq h4.panel-title a {
    display: block;
    padding: 10px 15px;
    font-size: 18px;
    font-weight: 700;
    color: #e00445;
}

.faq .panel-body {
    border-top: 0;
    padding: 15px;
    border: 1px solid #ddd;
    border-top: 0;
}


/* Contact Form */

.contact-item {
    text-align: center;
    border: 1px solid #e1e1e1;
    padding: 30px 20px;
    margin-bottom: 20px;
}

.contact-icon {
    text-align: center;
    margin-bottom: 15px;
}

.contact-icon i {
    width: 40px;
    height: 40px;
    color: #e00445;
}

.contact-icon i {
    font-size: 34px;
}

.contact-text {
    margin-top: 0px;
}

.contact-text h4 {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 5px;
}

.contact-text p {
    margin: 0;
}

.contact-form textarea {
    height: 190px
}

.contact-form .btn {
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    padding: 10px 20px;
    border: 1px solid #e00445;
    background: #e00445;
    color: #fff;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
    border-radius: 8px;
    text-transform: none;
}

.contact-form .btn:hover {
    color: #fff;
    background: #313131;
    border: 1px solid #313131;
}

.map-area iframe {
    width: 100%;
    height: 450px;
    margin-bottom: -5px;
}

h4.contact-form-title {
    font-weight: 700;
    font-size: 24px;
}

.contact-form button {
    background: #e00445;
    border: 2px solid #e00445;
    border-radius: 50px;
    padding: 10px 30px;
    display: inline-block;
    color: #fff;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
    font-weight: 600;
    text-transform: uppercase;
}

.contact-form button:hover {
    background: transparent;
    border-color: #fff;
}


/* Photo Gallery */

.gallery-photo {
    margin-bottom: 30px;
}

.gallery-photo img {
    width: 100%;
    height: auto;
}

.gallery-photo .gallery-photo-bg {
    position: absolute;
    left: 15px;
    top: 0;
    width: calc(100% - 30px);
    height: calc(100% - 30px);
    opacity: 0;
    background: #333;
    z-index: 9;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

.gallery-photo:hover .gallery-photo-bg {
    opacity: 0.6;
}

.gallery-photo .plus-icon {
    visibility: hidden;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    line-height: 100%;
    text-align: center;
    z-index: 99;
    font-size: 30px;
    color: #fff;
}

.gallery-photo:hover .plus-icon {
    visibility: visible;
    display: flex;
    align-items: center;
    justify-content: center;
}


/* Video Gallery */

.video-item {
    margin-bottom: 30px;
}

.video-item iframe {
    width: 100%;
    height: 350px;
}


/* Product */

.product-item {
    margin-bottom: 50px;
}

.product-item .photo img {
    width: 100%;
    height: auto;
}

.product-item .text h3 {
    font-size: 20px;
    font-weight: 700;
    margin-top: 15px;
}

.product-item .text h3 a {
    color: #333;
}

.product-item .text h3 a:hover {
    color: #e00445;
}

.product-item .text .price {
    margin-bottom: 15px;
    font-weight: 500;
    font-size: 18px;
}

.product-item .text .price del {
    color: red;
}

.product-item .text .cart-button a {
    background: #e00445;
    color: #fff;
    border-radius: 30px;
    padding: 10px 20px;
    font-size: 14px;
}

.product-item .text .cart-button a:hover {
    background: #333;
}


/* Product Detail */

.product-detail .photo img {
    width: 100%;
}

.product-detail h2 {
    font-size: 22px;
    font-weight: 700;
}

.product-detail p {
    font-size: 16px;
}

.product-detail .price {
    font-size: 28px;
    font-weight: 600;
    color: #e00445;
}

.product-detail .price del {
    color: red;
}

.product-detail .delivery-time {
    font-size: 18px;
}

.product-detail .qty {
    font-size: 18px;
}

.product-detail .qty input[type="number"] {
    width: 100px;
}

.product-detail button {
    cursor: pointer;
    display: inline-block;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    padding: 14px 30px;
    border: 0;
    border-radius: 50px;
    background: #e00445;
    color: #fff;
    -webkit-transition: all 0.3s;
    transition: all 0.3s;
}

.product-detail button:hover {
    background: #333;
}

.product-detail .nav-pills .nav-link.active {
    background: #e00445;
    color: #fff;
}

.product-detail .nav-pills .nav-link {
    background: #f1f5f9;
    color: #e00445;
}

.product-detail li.nav-item {
    margin-right: 10px;
}


/* Cart */

.cart table img {
    width: 100px;
    height: auto;
}

.cart input[type="number"] {
    width: 100px;
}

.cart .total {
    font-size: 18px;
    font-weight: 600;
}

.cart-buttons {
    display: flex;
    justify-content: flex-start;
}

.cart-buttons a {
    margin-right: 10px;
}

.checkout-login-form h2 {
    font-size: 24px;
    font-weight: 700;
}

.checkout-login-form .inner {
    display: flex;
    justify-content: center;
}

.checkout-login-form input {
    width: 400px;
}

.checkout-login-form .new-user {
    margin-top: 15px;
    font-weight: 600;
}

.checkout-login-form .new-user a {
    color: #e00445;
}

.checkout-login-form .new-user a:hover {
    color: #333;
}

.checkout h2 {
    font-size: 24px;
    font-weight: 700;
}

.checkout-billing h2 {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 20px;
}


/* Login */
.reg-login-form h2 {
    font-size: 24px;
    font-weight: 700;
}

.reg-login-form .inner {
    display: flex;
    justify-content: center;
}

.reg-login-form input {
    width: 400px;
}

.reg-login-form .new-user {
    margin-top: 15px;
    font-weight: 600;
}

.reg-login-form .new-user a {
    color: #e00445;
}

.reg-login-form .new-user a:hover {
    color: #333;
}

.reg-login-form button {
    background: #e00445;
    border: 0;
}

.reg-login-form button:hover {
    background: #333;
}

.reg-login-form .link {
    color: #e00445;
}

@media only screen and (max-width: 575px) {
    .reg-login-form input {
        width: 100%;
    }
    .reg-login-form .inner {
        display: block;
    }
}


/* User Panel */

.user-sidebar ul {
    display: flex;
    flex-direction: column;
}

.user-sidebar ul li {
    list-style-type: none;
    margin-bottom: 5px;
}

.user-sidebar ul li a {
    background: #7b7c7d!important;
    border: 0;
}

.user-sidebar ul li a:hover {
    background: #333!important;
}

.modal-table {
    font-size: 16px;
}

.modal-table tr th {
    width: 150px;
}

.chosen-drop {
    z-index: 999999 !important;
}


/* Listing Brand */

.listing-brand-item {
    margin-bottom: 25px;
}

.listing-brand-item .heading {
    background: #e00445;
    color: #fff;
    padding: 10px;
    font-weight: 700;
    font-size: 20px;
    overflow: hidden;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
}

.listing-brand-item .heading .name {
    float: left;
}

.listing-brand-item .heading .name a {
    color: #fff;
}

.listing-brand-item .heading .total {
    float: right;
}

.listing-brand-item .text {
    background: #f3f3f3;
    color: #333;
    padding: 10px;
    overflow: hidden;
    border-bottom: 1px solid #c0c0c0;
}

.listing-brand-item .text:last-child {
    border-bottom: 0;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
}

.listing-brand-item .text .name {
    float: left;
}

.listing-brand-item .text .name a {
    color: #333;
}

.listing-brand-item .text .name a:hover {
    color: #e00445;
}

.listing-brand-item .text .total {
    float: right;
}


/* Listing Single Page */

.listing-single-banner {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    position: relative;
    padding-top: 100px;
    padding-bottom: 100px;
}

.listing-single-banner .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: #4b4b4b;
}

.listing-single-banner h1 {
    color: #fff;
    font-size: 50px;
    font-weight: 700;
}

.listing-single-banner .price {
    color: #fff;
    font-size: 28px;
    font-weight: 600;
    margin-top: 15px;
}

.listing-single-banner .location {
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    margin-top: 15px;
}

.listing-single-banner .review {
    color: #fbc715;
    font-size: 16px;
    margin-top: 5px;
    font-weight: 600;
}

.listing-single-banner .call {
    font-size: 18px;
    color: #fff;
    font-weight: 700;
    margin-top: 15px;
}

.listing-single-banner .listing-items {
    margin-top: 20px;
    margin-bottom: 30px;
}

.listing-single-banner .listing-items a {
    background: #fff;
    color: #e00445;
    padding: 6px 15px;
    border: 1px solid #fff;
    margin-right: 10px;
    border-radius: 4px;
    display: inline-block;
}

.listing-single-banner .listing-items a:hover {
    background: transparent;
    border: 1px solid #fff;
    color: #fff;
}

.listing-single-banner .social ul li {
    margin: 0;
    list-style-type: none;
    display: inline-block;
}

.listing-single-banner .social a {
    color: #fff;
    margin-right: 5px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 4px;
    background: transparent;
    border: 1px solid #fff;
    display: block;
}

.listing-single-banner .social a:hover {
    background: #e00445;
    color: #fff;
}

@media only screen and (max-width: 991px) {
    .listing-single-banner {
        padding-top: 150px;
    }
}

@media only screen and (max-width: 767px) {
    .listing-single-banner .listing-items a {
        margin-bottom: 15px;
    }
    .listing-single-banner .listing-items {
        margin-bottom: 15px;
    }
}

.listing-page h2 {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 15px;
}

.listing-page h2 i {
    color: #e00445;
}

.listing-page .sep {
    display: block;
    border-top: 1px solid #c7c7c7;
    margin-top: 40px;
    margin-bottom: 40px;
}

.listing-page .gap {
    display: block;
    margin-top: 40px;
    margin-bottom: 40px;
}

.listing-page .room-all .item {
    border: 1px solid #d2d2d2;
    margin-bottom: 30px;
}

.listing-page .room-all .item img {
    width: 100%;
    height: 120px;
    object-fit: cover;
}
.listing-page .room-all .item .text {
    padding: 5px 10px 10px 10px;
}
.listing-page .room-all .item h3 {
    font-size: 18px;
    font-weight: 700;
}
.listing-page .room-all .item .price {
    font-weight: 700;
    font-size: 24px;
}
.listing-page .room-all .item .price span {
    font-size: 14px;
    color: #969696;
}

.listing-page .photo-all .item {
    position: relative;
    margin-bottom: 25px;
}

.listing-page .photo-all img {
    width: 100%;
    height: auto;
    height: 140px;
    object-fit: cover;
    border-radius: 6px;
}

.listing-page .video-all .item {
    position: relative;
    margin-bottom: 25px;
}

.listing-page .video-all img {
    width: 100%;
    height: auto;
    height: 140px;
    object-fit: cover;
    border-radius: 6px;
}

.listing-page .photo-all .item .icon,
.listing-page .video-all .item .icon {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 99;
}

.listing-page .photo-all .item .icon {
    font-size: 20px;
}

.listing-page .video-all .item .icon {
    font-size: 40px;
}

.listing-page .photo-all .item .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #333;
    opacity: 0.3;
    border-radius: 6px;
}

.listing-page .video-all .item .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #333;
    opacity: 0.3;
    border-radius: 6px;
}

.listing-page table tr th,
.listing-page table tr td {
    border-color: #c4c4c4!important;
}

@media only screen and (max-width: 1199px) {
    .listing-page .photo-all img,
    .listing-page .video-all img {
        height: 120px;
    }
}

@media only screen and (max-width: 991px) {
    .listing-page .photo-all img,
    .listing-page .video-all img,
    .listing-page .room-all .item img {
        height: 190px;
    }
}

@media only screen and (max-width: 767px) {
    .listing-page .photo-all img,
    .listing-page .video-all img,
    .listing-page .room-all .item img {
        height: 320px;
    }
}

@media only screen and (max-width: 575px) {
    .listing-page .photo-all img,
    .listing-page .video-all img,
    .listing-page .room-all .item img {
        height: 320px;
    }
}

@media only screen and (max-width: 475px) {
    .listing-page .photo-all img,
    .listing-page .video-all img,
    .listing-page .room-all .item img {
        height: 280px;
    }
}

@media only screen and (max-width: 400px) {
    .listing-page .photo-all img,
    .listing-page .video-all img,
    .listing-page .room-all .item img {
        height: 220px;
    }
}

.listing-page .map iframe {
    width: 100%;
    height: 400px;
}

@media only screen and (max-width: 991px) {
    .listing-page .map iframe {
        height: 220px;
    }
}

@media only screen and (max-width: 767px) {
    .listing-page .map iframe {
        height: 320px;
    }
}

.listing-page .amenities li {
    list-style-type: none;
    width: 33.33%;
    display: inline-block;
    margin-right: -4px;
    font-size: 16px;
    margin-bottom: 10px;
}

.listing-page .amenities li i {
    color: #e00445;
}

@media only screen and (max-width: 991px) {
    .listing-page .amenities li {
        width: 100%;
    }
}

.listing-page .contact a {
    color: #e00445;
}

.listing-page .review-overall {
    margin-top: 5px;
    font-weight: 700;
    margin-bottom: 40px;
}

.listing-page .review-overall .review {
    display: inline-block;
    color: #fbc715;
    font-size: 16px;
}

.listing-page .review-overall .total {
    display: inline-block;
    margin-left: 10px;
    color: #e00445;
    font-size: 16px;
}

.listing-page .reviews .item {
    margin-bottom: 40px;
}

.listing-page .reviews .photo {
    margin-bottom: 10px;
}

.listing-page .reviews .photo img {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.listing-page .reviews .name {
    font-size: 18px;
    font-weight: 700;
}

.listing-page .reviews .score {
    color: #fbc715;
    margin-bottom: 15px;
}

.listing-page .reviews .date-time {
    font-size: 16px;
}

.listing-page .review-form button {
    background: #e00445;
    border: 0;
}

.listing-page a.login-to-review {
    color: #e00445;
}

@media only screen and (max-width: 767px) {
    .listing-sidebar {
        margin-top: 50px;
    }
}

.listing-sidebar .ls-widget {
    background: #f3f3f3;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
}

.listing-sidebar .ls-widget h2 {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #c7c7c7;
}

.listing-sidebar .ls-widget h2 i {
    color: #e00445;
}

.listing-sidebar .ls-widget .agent {
    overflow: hidden;
}

.listing-sidebar .ls-widget .agent .photo {
    display: inline-block;
    margin-right: -4px;
}

.listing-sidebar .ls-widget .agent .photo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.listing-sidebar .ls-widget .agent .text {
    display: inline-block;
    margin-right: -4px;
    vertical-align: middle;
    padding-left: 10px;
}

.listing-sidebar .ls-widget .agent .text h3 {
    font-size: 18px;
    font-weight: 700;
}

.listing-sidebar .ls-widget .agent .text h3 a {
    color: #333;
}

.listing-sidebar .ls-widget .agent .text h4 {
    font-size: 16px;
}

.listing-sidebar .ls-widget .agent-contact ul {
    margin-top: 20px;
}

.listing-sidebar .ls-widget .agent-contact li {
    list-style-type: none;
    margin-bottom: 10px;
    color: #e00445;
}

.listing-sidebar .ls-widget .agent-contact li a {
    color: #e00445;
}

.listing-sidebar .ls-widget .agent-social {
    border-top: 1px solid #c7c7c7;
    border-bottom: 1px solid #c7c7c7;
    padding-top: 20px;
}

.listing-sidebar .ls-widget a.agent-view-profile {
    background: #e00445;
    border: 0;
    border-top: 1px solid #c7c7c7;
    margin-top: 20px;
}

.listing-sidebar .ls-widget .agent-social ul li {
    margin: 0;
    list-style-type: none;
    display: inline-block;
}

.listing-sidebar .ls-widget .agent-social ul li a {
    color: #fff;
    margin-right: 5px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 4px;
    background: #e00445;
    border: 0;
    display: block;
}

.listing-sidebar .ls-widget .agent-social ul li a:hover {
    background: #333;
    color: #fff;
}

.listing-sidebar .ls-widget .openning-hour table {
    background: #fff;
}

.listing-sidebar .ls-widget .category ul li {
    list-style-type: none;
    margin-bottom: 10px;
}

.listing-sidebar .ls-widget .category ul li a {
    color: #e00445;
}


/* Agent Page */

.agent-banner {
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    position: relative;
    padding-top: 50px;
    padding-bottom: 50px;
}

.agent-banner .bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.5;
    background: #4b4b4b;
}

.agent-banner h1 {
    color: #fff;
    font-size: 50px;
    font-weight: 700;
}

.agent-banner .agent {
    overflow: hidden;
}

.agent-banner .agent .photo {
    display: inline-block;
    margin-right: -4px;
}

.agent-banner .agent .photo img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}

.agent-banner .agent .text {
    display: inline-block;
    margin-right: -4px;
    vertical-align: middle;
    padding-left: 10px;
}

.agent-banner .agent .text h3 {
    font-size: 18px;
    font-weight: 700;
    color: #fff;
}

.agent-banner .agent .text h4 {
    font-size: 16px;
    color: #fff;
}

.agent-banner .contact {
    font-size: 18px;
    color: #fff;
    font-weight: 700;
    float: right;
}

.agent-banner .contact .item {
    margin-bottom: 10px;
}

.agent-banner .social {
    margin-top: 20px;
}

.agent-banner .social ul li {
    margin: 0;
    list-style-type: none;
    display: inline-block;
}

.agent-banner .social a {
    color: #fff;
    margin-right: 5px;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    border-radius: 4px;
    background: transparent;
    border: 1px solid #fff;
    display: block;
}

.agent-banner .social a:hover {
    background: #e00445;
    color: #fff;
}

@media only screen and (max-width: 991px) {
    .agent-banner {
        padding-top: 150px;
    }
    .agent-banner .contact {
        float: left;
    }
}

@media only screen and (max-width: 767px) {
    .agent-banner .listing-items a {
        margin-bottom: 15px;
    }
    .agent-banner .listing-items {
        margin-bottom: 15px;
    }
}

.listing-filter .lf-heading {
    color: #e00445;
    padding-bottom: 10px;
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 700;
    border-bottom: 1px solid #e00445;
}

.listing-filter .lf-widget {
    background: #f3f3f3;
    padding: 20px;
    border-radius: 6px;
    margin-bottom: 25px;
}

.listing-filter .lf-widget h2 {
    font-weight: 700;
    font-size: 24px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #c7c7c7;
}

.listing-filter .lf-widget .form-check {
    margin-bottom: 10px;
}

.filter-button {
    background: #e00445;
    color: #fff;
    border: 0;
    font-weight: 700;
    font-size: 22px;
}

img.listing-photo-item {
    width: 100%;
    height: 130px;
    object-fit: cover;
}

.invoice-area img {
    height: 90px;
}

.invoice-area .company-info {
    font-size: 16px;
}

.invoice-head {
    margin-bottom: 30px;
    border-bottom: 1px solid #efebeb;
    padding-bottom: 20px;
}

.invoice-head .iv-left span {
    color: #444;
}

.invoice-head span {
    font-size: 21px;
    font-weight: 700;
    color: #777;
}

.invoice-address h3 {
    font-size: 24px;
    text-transform: uppercase;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.invoice-address h5 {
    font-size: 17px;
    margin-bottom: 10px;
}

.invoice-address p {
    font-size: 13px;
    color: #555;
    margin-bottom: 3px;
}

.invoice-date li {
    font-size: 15px;
    color: #555;
    font-weight: 700;
    margin-bottom: 5px;
}

.invoice-table .table-bordered td,
.invoice-table .table-bordered th {
    border: 1px solid rgba(120, 130, 140, 0.13) !important;
    border-left: none!important;
    border-right: none!important;
    font-size: 13px!important;
}

.invoice-table tr td {
    color: #666;
}

.invoice-table tfoot tr td {
    text-transform: uppercase;
    font-weight: 600;
    color: #444;
}

.invoice-buttons a {
    display: inline-block;
    font-size: 15px;
    color: #fff;
    background: #815ef6;
    padding: 12px 19px;
    border-radius: 3px;
    text-transform: capitalize;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    letter-spacing: 0.03em;
    margin-left: 6px;
}

.invoice-buttons a:hover {
    background: #574494;
}

@media print {
    .page-banner,
    .invoice-sidebar,
    .footer-area,
    .print-invoice-button {
        display: none!important;
    }
    .invoice-right {
        max-width: 100%!important;
        flex: 0 0 100%!important;
    }
}

.cke_dialog_ui_hbox_last,
a.cke_dialog_ui_button.cke_dialog_image_browse {
    display: none!important;
}

.ad-section {
    margin-top: 50px;
}

.ad-section .inner img {
    width: 100%;
    height: auto;
}

.stripe-button-el,
.stripe-button-el span,
.razorpay-payment-button,
.flutterwave-button,
.mollie-button {
    background-image: none!important;
    background-color: #009cde!important;
    border: 0!important;
    font-weight: 600;
    font-size: 14px;
    line-height: 24px;
    padding: 5px 10px;
    color: #fff;
    border-style: none;
    border-radius: 4px;
    width: 100%;
    text-align: center;
    box-shadow: none!important;
}

.stripe-button-el span {
    text-shadow: none!important;
    width: 100%!important;
    box-shadow: none!important;
}

.testimonial {
    margin-top: 50px;
    padding-top: 50px;
    padding-bottom: 50px;
    overflow: hidden;
    background: #33B836;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    background-attachment: fixed;
    position: relative;
}

.testimonial-bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #0d263b;
    opacity: 0.75;
}

.testimonial .heading {
    text-align: center;
    margin-bottom: 40px;
}

.testimonial .heading h2 {
    font-size: 30px;
    font-weight: 700;
    color: #fff;
    margin-top: 0;
}

.testimonial .heading h3 {
    font-size: 18px;
    font-weight: 400;
    color: #fff;
}

.testimonial .testimonial-item .photo {
    text-align: center;
}

.testimonial .testimonial-item .photo img {
    width: 80px;
    height: 80px;
    display: inline-block;
}

.testimonial .testimonial-item .text {
    padding: 20px 150px;
    text-align: center;
}

.testimonial .testimonial-item .text h3 {
    font-size: 20px;
    font-weight: 700;
    color: #fff;
    position: relative;
    margin-top: 40px;
}

.testimonial .testimonial-item .text h3:before {
    content: '';
    position: absolute;
    left: calc(50% - 20px);
    top: -20px;
    width: 40px;
    height: 2px;
    background: #fff;
}

.testimonial .testimonial-item .text h4 {
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #fff;
}

.testimonial .testimonial-item .text p {
    font-size: 15px;
    color: #fff;
}

.testimonial .owl-dots {
    text-align: center;
    margin-top: 5px;
}

.testimonial .owl-dots .owl-dot {
    width: 24px;
    height: 24px;
    background: #fff;
    border: 7px solid #fff;
    border-radius: 50%;
    display: inline-block;
    margin: 0 5px;
}

.testimonial .owl-dots .owl-dot.active {
    width: 24px;
    height: 24px;
    background: #333;
    border: 7px solid #fff;
}

@media only screen and (max-width: 992px) {
    .testimonial .testimonial-item .text {
        padding-left: 10px;
        padding-right: 10px;
    }
}



.image-effect {
	position: relative;
	display: block;
	vertical-align: top;
	max-width: 100%;
}

.image-effect:before,
.image-effect:after {
	position: absolute;
	content: '';
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(255, 255, 255, .3);
	z-index: 6;
	-webkit-transition: transform .5s, opacity .2s ease-in-out 0s;
	-o-transition: transform .5s, opacity .2s ease-in-out 0s;
	transition: transform .5s, opacity .2s ease-in-out 0s;
	opacity: 0;
	filter: alpha(opacity=0);
}

.effect-item:hover .image-effect:before {
	-webkit-transform: scale(0, 1);
	-ms-transform: scale(0, 1);
	-o-transform: scale(0, 1);
	transform: scale(0, 1);
	opacity: 1;
	filter: alpha(opacity=100);
}

.effect-item:hover .image-effect:after {
	-webkit-transform: scale(1, 0);
	-ms-transform: scale(1, 0);
	-o-transform: scale(1, 0);
	transform: scale(1, 0);
	opacity: 1;
	filter: alpha(opacity=100);
}


/* Home Video */
.home-video {
    margin-top: 40px;
	padding-top: 120px;
	padding-bottom: 100px;
	background: #4ab04d;
	overflow: hidden;
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center center;
	background-attachment: fixed;
	position: relative;
}

.home-video .bg {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background: #0d263b;
	opacity: 0.7;
}

.home-video h2 {
	color: #fff;
	text-align: center;
	margin-top: 0;
	font-weight: 700;
	font-size: 40px;
	margin-bottom: 20px;
}

.home-video p {
	color: #fff;
	text-align: center;
	padding-left: 100px;
	padding-right: 100px;
}

.home-video .video-section {
	text-align: center;
}

.home-video .video-section a {
	font-size: 100px;
	color: #fff;
}