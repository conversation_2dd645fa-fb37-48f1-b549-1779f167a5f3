@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerEditProfileViewModel
@{
    ViewData["Title"] = "Edit Profile Information";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOther?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Edit Profile Information</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Edit Profile Information</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <form action="@Url.Action("EditProfile", "Customer")" method="post">
                    @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Name</label>
                                <input type="text" class="form-control" asp-for="Customer.Name" required>
                                <span asp-validation-for="Customer.Name" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Email Address</label>
                                <input type="email" class="form-control" asp-for="Customer.Email" required>
                                <span asp-validation-for="Customer.Email" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Phone</label>
                                <input type="text" class="form-control" asp-for="Customer.Phone">
                                <span asp-validation-for="Customer.Phone" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Country</label>
                                <input type="text" class="form-control" asp-for="Customer.Country">
                                <span asp-validation-for="Customer.Country" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Address</label>
                                <input type="text" class="form-control" asp-for="Customer.Address">
                                <span asp-validation-for="Customer.Address" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">State</label>
                                <input type="text" class="form-control" asp-for="Customer.State">
                                <span asp-validation-for="Customer.State" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">City</label>
                                <input type="text" class="form-control" asp-for="Customer.City">
                                <span asp-validation-for="Customer.City" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">ZIP Code</label>
                                <input type="text" class="form-control" asp-for="Customer.ZipCode">
                                <span asp-validation-for="Customer.ZipCode" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Website</label>
                                <input type="url" class="form-control" asp-for="Customer.Website">
                                <span asp-validation-for="Customer.Website" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Facebook</label>
                                <input type="url" class="form-control" asp-for="Customer.Facebook">
                                <span asp-validation-for="Customer.Facebook" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Twitter</label>
                                <input type="url" class="form-control" asp-for="Customer.Twitter">
                                <span asp-validation-for="Customer.Twitter" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">LinkedIn</label>
                                <input type="url" class="form-control" asp-for="Customer.LinkedIn">
                                <span asp-validation-for="Customer.LinkedIn" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Instagram</label>
                                <input type="url" class="form-control" asp-for="Customer.Instagram">
                                <span asp-validation-for="Customer.Instagram" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Pinterest</label>
                                <input type="url" class="form-control" asp-for="Customer.Pinterest">
                                <span asp-validation-for="Customer.Pinterest" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">YouTube</label>
                                <input type="url" class="form-control" asp-for="Customer.Youtube">
                                <span asp-validation-for="Customer.Youtube" class="text-danger"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Bio</label>
                                <textarea class="form-control" asp-for="Customer.Bio" rows="3"></textarea>
                                <span asp-validation-for="Customer.Bio" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
