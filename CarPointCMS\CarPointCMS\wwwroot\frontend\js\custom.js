(function ($) {

	"use strict";

	$( "#timepicker" ).timepicker();

	// Scroll-Top
	$(".scroll-top").hide();
	$(window).on("scroll", function () {
		if ($(this).scrollTop() > 300) {
			$(".scroll-top").fadeIn();
		} else {
			$(".scroll-top").fadeOut();
		}
	});
	$(".scroll-top").on("click", function () {
		$("html, body").animate({
			scrollTop: 0,
		}, 700)
	});

	$('#example').DataTable();

	$(document).ready(function() {

		$('.select2').select2({
			theme: "bootstrap"
		});

	    $('.paypal').hide();
	    $('.stripe').hide();
	    $('.bank').hide();
	    $('.cash-on-delivery').hide();

		$('#paymentMethodChange').on('change',function() {

		    if($('#paymentMethodChange').val() == 'PayPal')
		    {
		        $('.paypal').show();
		        $('.stripe').hide();
		        $('.bank').hide();
		        $('.cash-on-delivery').hide();
		    }
		    else if($('#paymentMethodChange').val() == 'Stripe')
		    {
		        $('.paypal').hide();
		        $('.stripe').show();
		        $('.bank').hide();
		        $('.cash-on-delivery').hide();
		    }
		    else if($('#paymentMethodChange').val() == 'Bank')
		    {
		    	$('.paypal').hide();
		        $('.stripe').hide();
		        $('.bank').show();
		        $('.cash-on-delivery').hide();
		    }
		    else if($('#paymentMethodChange').val() == 'Cash On Delivery')
		    {
		    	$('.paypal').hide();
		        $('.stripe').hide();
		        $('.bank').hide();
		        $('.cash-on-delivery').show();
		    }
		    else if($('#paymentMethodChange').val() == '')
		    {
		    	$('.paypal').hide();
		        $('.stripe').hide();
		        $('.bank').hide();
		        $('.cash-on-delivery').hide();
		    }

		});
	});


	// Wow Active
	new WOW().init();

	// Mean Menu
	jQuery('.mean-menu').meanmenu({
		meanScreenWidth: "991"
	});

	// Video Popup
	$('.video-button').magnificPopup({
	  	type: 'iframe',
		gallery:{
			enabled:true
		}
	});

	$('.magnific').magnificPopup({
	  	type: 'image',
		gallery:{
			enabled:true
		}
	});

	$('.my').iconpicker();

	if($(window).width() > 767) {
		$("#sticky_sidebar").stickit({
			top: 80,
		})
	}

	tinymce.init({
        selector: '.editor',
        height : '480'
    });

    // Modern Sidebar Functionality
    $(document).ready(function() {
        // Highlight current page in sidebar
        function highlightCurrentPage() {
            var currentPath = window.location.pathname.toLowerCase();
            var currentAction = '';

            // Extract action from path
            if (currentPath.includes('/dashboard')) {
                currentAction = 'dashboard';
            } else if (currentPath.includes('/packages')) {
                currentAction = 'packages';
            } else if (currentPath.includes('/purchasehistory')) {
                currentAction = 'purchasehistory';
            } else if (currentPath.includes('/mylistings')) {
                currentAction = 'mylistings';
            } else if (currentPath.includes('/addlisting')) {
                currentAction = 'addlisting';
            } else if (currentPath.includes('/myreviews')) {
                currentAction = 'myreviews';
            } else if (currentPath.includes('/wishlist')) {
                currentAction = 'wishlist';
            } else if (currentPath.includes('/editprofile')) {
                currentAction = 'editprofile';
            } else if (currentPath.includes('/editpassword')) {
                currentAction = 'editpassword';
            } else if (currentPath.includes('/editphoto')) {
                currentAction = 'editphoto';
            } else if (currentPath.includes('/editbanner')) {
                currentAction = 'editbanner';
            }

            // Remove active class from all nav links
            $('.nav-link').removeClass('current-page');

            // Add active class to current page link
            $('.nav-link').each(function() {
                var href = $(this).attr('href').toLowerCase();
                if (currentAction && href.includes(currentAction)) {
                    $(this).addClass('current-page');
                }
            });
        }

        // Initialize sidebar highlighting
        highlightCurrentPage();

        // Add smooth hover effects
        $('.nav-link').hover(
            function() {
                if (!$(this).hasClass('current-page')) {
                    $(this).find('.nav-icon').addClass('text-primary');
                }
            },
            function() {
                if (!$(this).hasClass('current-page')) {
                    $(this).find('.nav-icon').removeClass('text-primary');
                }
            }
        );

        // Add click animation
        $('.nav-link').on('click', function() {
            $(this).addClass('clicked');
            setTimeout(() => {
                $(this).removeClass('clicked');
            }, 200);
        });
    });

})(jQuery);
