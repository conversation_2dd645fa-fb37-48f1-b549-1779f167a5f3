@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerDashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOther?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Dashboard</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Dashboard</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Html.PartialAsync("_CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <!-- Welcome Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="welcome-card">
                            <div class="welcome-content">
                                <h2 class="welcome-title">Welcome back, @Model.Customer?.Name!</h2>
                                <p class="welcome-subtitle">Here's what's happening with your listings today.</p>
                            </div>
                            <div class="welcome-actions">
                                <a href="@Url.Action("AddListing", "Customer", new { area = "Customer" })" class="btn btn-primary btn-modern">
                                    <i class="fas fa-plus"></i> Add New Listing
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card stats-card-primary">
                            <div class="stats-icon">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="stats-content">
                                <h3 class="stats-number">@Model.TotalActiveListings</h3>
                                <p class="stats-label">Active Listings</p>
                            </div>
                            <div class="stats-action">
                                <a href="@Url.Action("MyListings", "Customer", new { area = "Customer" })" class="btn btn-sm btn-outline-primary">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card stats-card-warning">
                            <div class="stats-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stats-content">
                                <h3 class="stats-number">@Model.TotalPendingListings</h3>
                                <p class="stats-label">Pending Approval</p>
                            </div>
                            <div class="stats-action">
                                <a href="@Url.Action("MyListings", "Customer", new { area = "Customer" })" class="btn btn-sm btn-outline-warning">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card stats-card-success">
                            <div class="stats-icon">
                                <i class="fas fa-heart"></i>
                            </div>
                            <div class="stats-content">
                                <h3 class="stats-number">0</h3>
                                <p class="stats-label">Wishlist Items</p>
                            </div>
                            <div class="stats-action">
                                <a href="@Url.Action("Wishlist", "Customer", new { area = "Customer" })" class="btn btn-sm btn-outline-success">View All</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <div class="stats-card stats-card-info">
                            <div class="stats-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stats-content">
                                <h3 class="stats-number">0</h3>
                                <p class="stats-label">Reviews</p>
                            </div>
                            <div class="stats-action">
                                <a href="@Url.Action("MyReviews", "Customer", new { area = "Customer" })" class="btn btn-sm btn-outline-info">View All</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Package Information -->
                @if(Model.CurrentPackage != null)
                {
                    var daysRemaining = (Model.CurrentPackage.PackageEndDate - DateTime.Now).Days;
                    var totalDays = (Model.CurrentPackage.PackageEndDate - Model.CurrentPackage.PackageStartDate).Days;
                    var progressPercentage = totalDays > 0 ? Math.Max(0, Math.Min(100, ((totalDays - daysRemaining) * 100.0 / totalDays))) : 0;
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="package-card">
                                <div class="package-header">
                                    <div class="package-title">
                                        <h4><i class="fas fa-box"></i> Current Package</h4>
                                        <span class="package-name">@Model.CurrentPackage.Package?.PackageName</span>
                                    </div>
                                    <div class="package-status">
                                        @if(daysRemaining < 0)
                                        {
                                            <span class="badge badge-danger badge-lg">Expired</span>
                                        }
                                        else if(daysRemaining <= 7)
                                        {
                                            <span class="badge badge-warning badge-lg">Expiring Soon</span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-success badge-lg">Active</span>
                                        }
                                    </div>
                                </div>

                                <div class="package-body">
                                    <div class="row">
                                        <div class="col-md-8">
                                            <div class="package-details">
                                                <div class="detail-item">
                                                    <span class="detail-label">Start Date:</span>
                                                    <span class="detail-value">@Model.CurrentPackage.PackageStartDate.ToString("dd MMMM, yyyy")</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">End Date:</span>
                                                    <span class="detail-value">@Model.CurrentPackage.PackageEndDate.ToString("dd MMMM, yyyy")</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Listings Allowed:</span>
                                                    <span class="detail-value">@Model.CurrentPackage.Package?.TotalListings</span>
                                                </div>
                                                <div class="detail-item">
                                                    <span class="detail-label">Featured Listings:</span>
                                                    <span class="detail-value">
                                                        @if(Model.CurrentPackage.Package?.AllowFeatured == "Yes")
                                                        {
                                                            <span class="text-success"><i class="fas fa-check"></i> Allowed</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted"><i class="fas fa-times"></i> Not Allowed</span>
                                                        }
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="package-progress">
                                                <div class="progress-info">
                                                    <span class="progress-label">Package Duration</span>
                                                    <span class="progress-value">@Math.Max(0, daysRemaining) days remaining</span>
                                                </div>
                                                <div class="progress progress-modern">
                                                    <div class="progress-bar @(daysRemaining <= 7 ? "bg-warning" : "bg-success")"
                                                         role="progressbar"
                                                         style="width: @progressPercentage%"
                                                         aria-valuenow="@progressPercentage"
                                                         aria-valuemin="0"
                                                         aria-valuemax="100">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="package-footer">
                                    <a href="@Url.Action("Packages", "Customer", new { area = "Customer" })" class="btn btn-outline-primary">
                                        <i class="fas fa-shopping-cart"></i> Upgrade Package
                                    </a>
                                    <a href="@Url.Action("PurchaseHistory", "Customer", new { area = "Customer" })" class="btn btn-outline-secondary">
                                        <i class="fas fa-history"></i> Purchase History
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="no-package-card">
                                <div class="no-package-content">
                                    <i class="fas fa-box-open"></i>
                                    <h4>No Active Package</h4>
                                    <p>You don't have an active package. Purchase a package to start listing your vehicles.</p>
                                    <a href="@Url.Action("Packages", "Customer", new { area = "Customer" })" class="btn btn-primary btn-lg">
                                        <i class="fas fa-shopping-cart"></i> Browse Packages
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                }

                <!-- Quick Actions -->
                <div class="row">
                    <div class="col-12">
                        <div class="quick-actions-card">
                            <h4 class="card-title"><i class="fas fa-bolt"></i> Quick Actions</h4>
                            <div class="quick-actions-grid">
                                <a href="@Url.Action("AddListing", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-primary">
                                        <i class="fas fa-plus"></i>
                                    </div>
                                    <span class="quick-action-label">Add Listing</span>
                                </a>
                                <a href="@Url.Action("MyListings", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-info">
                                        <i class="fas fa-list"></i>
                                    </div>
                                    <span class="quick-action-label">My Listings</span>
                                </a>
                                <a href="@Url.Action("EditProfile", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-success">
                                        <i class="fas fa-user-edit"></i>
                                    </div>
                                    <span class="quick-action-label">Edit Profile</span>
                                </a>
                                <a href="@Url.Action("Wishlist", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-danger">
                                        <i class="fas fa-heart"></i>
                                    </div>
                                    <span class="quick-action-label">Wishlist</span>
                                </a>
                                <a href="@Url.Action("MyReviews", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-warning">
                                        <i class="fas fa-star"></i>
                                    </div>
                                    <span class="quick-action-label">My Reviews</span>
                                </a>
                                <a href="@Url.Action("Packages", "Customer", new { area = "Customer" })" class="quick-action-item">
                                    <div class="quick-action-icon bg-secondary">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <span class="quick-action-label">Packages</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
