@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerPackagesViewModel
@{
    ViewData["Title"] = "Packages";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOtherItem?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Packages</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Packages</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <div class="row pricing">
                    @foreach(var package in Model.Packages)
                    {
                        <div class="col-lg-4">
                            <div class="card mb-5 mb-lg-0">
                                <div class="card-body">
                                    <h5 class="card-title text-muted text-uppercase text-center">@package.PackageName</h5>
                                    <h6 class="card-price text-center">
                                        @if(string.IsNullOrEmpty(Model.CurrentCurrencySymbol))
                                        {
                                            @($"${package.PackagePrice:F2}")
                                        }
                                        else
                                        {
                                            @($"{Model.CurrentCurrencySymbol}{(package.PackagePrice * Model.CurrentCurrencyValue):F2}")
                                        }
                                        <span class="period">/@package.ValidDays Days</span>
                                    </h6>
                                    <hr>
                                    <ul class="fa-ul">
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalListings Listing Allowed</li>
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalAmenities Amenities Per Listing</li>
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalPhotos Photos Per Listing</li>
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalVideos Videos Per Listing</li>
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalSocialItems Social Items Per Listing</li>
                                        <li><span class="fa-li"><i class="fas fa-check"></i></span>@package.TotalAdditionalFeatures Additional Features Per Listing</li>
                                        <li>
                                            @if(package.AllowFeatured == "Yes")
                                            {
                                                <span class="fa-li"><i class="fas fa-check"></i></span>
                                                @("Featured Listing Allowed")
                                            }
                                            else
                                            {
                                                <span class="fa-li"><i class="fas fa-times"></i></span>
                                                @("Featured Listing Not Allowed")
                                            }
                                        </li>
                                    </ul>

                                    @if(package.PackageType == "Free")
                                    {
                                        <a href="@Url.Action("EnrollFreePackage", "Customer", new { id = package.Id })" class="btn btn-block btn-primary">
                                            Enroll Now
                                        </a>
                                    }
                                    else
                                    {
                                        <a href="@Url.Action("BuyPackage", "Customer", new { id = package.Id })" class="btn btn-block btn-primary">
                                            Buy Now
                                        </a>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
