using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using CarPointCMS.Data;
using CarPointCMS.Models.Entities;
using System.Security.Claims;

namespace CarPointCMS.ViewComponents
{
    public class CustomerSidebarViewComponent : ViewComponent
    {
        private readonly ApplicationDbContext _context;

        public CustomerSidebarViewComponent(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var customer = await GetCurrentCustomerAsync();
            return View(customer);
        }

        private async Task<User?> GetCurrentCustomerAsync()
        {
            if (!User.Identity?.IsAuthenticated ?? true)
                return null;

            var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userIdClaim) || !int.TryParse(userIdClaim, out int userId))
                return null;

            return await _context.Users.FirstOrDefaultAsync(u => u.Id == userId);
        }
    }
}
