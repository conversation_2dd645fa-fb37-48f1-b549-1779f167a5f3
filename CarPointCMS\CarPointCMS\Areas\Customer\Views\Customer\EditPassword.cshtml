@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerEditPasswordViewModel
@{
    ViewData["Title"] = "Edit Password";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOtherItem?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Edit Password</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Edit Password</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <form action="@Url.Action("EditPassword", "Customer")" method="post">
                    @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Current Password</label>
                                <input type="password" class="form-control" name="CurrentPassword" asp-for="CurrentPassword" required>
                                <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">New Password</label>
                                <input type="password" class="form-control" name="Password" asp-for="Password" required>
                                <span asp-validation-for="Password" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Retype Password</label>
                                <input type="password" class="form-control" name="RePassword" asp-for="RePassword" required>
                                <span asp-validation-for="RePassword" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
