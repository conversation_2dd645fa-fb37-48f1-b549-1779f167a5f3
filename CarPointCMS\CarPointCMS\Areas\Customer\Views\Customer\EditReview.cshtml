@using CarPointCMS.Models.Entities
@using CarPointCMS.Models.ViewModels
@model CustomerEditReviewViewModel
@{
    ViewData["Title"] = "Edit Review";
}

<div class="page-banner" style="background-image: url('~/uploads/page_banners/@Model.PageOtherItem?.CustomerPanelPageBanner')">
    <div class="page-banner-bg"></div>
    <h1>Edit Review</h1>
    <nav>
        <ol class="breadcrumb justify-content-center">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">Home</a></li>
            <li class="breadcrumb-item active">Edit Review</li>
        </ol>
    </nav>
</div>

<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="user-sidebar">
                    @await Component.InvokeAsync("CustomerSidebar")
                </div>
            </div>
            <div class="col-md-9">
                <form action="@Url.Action("EditReview", "Customer", new { id = Model.Review.Id })" method="post">
                    @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="">Rating</label>
                                <select asp-for="Review.Rating" class="form-control">
                                    @for(int i = 1; i <= 5; i++)
                                    {
                                        <option value="@i">@i Star@(i > 1 ? "s" : "")</option>
                                    }
                                </select>
                                <span asp-validation-for="Review.Rating" class="text-danger"></span>
                            </div>
                            <div class="form-group">
                                <label for="">Review</label>
                                <textarea asp-for="Review.ReviewText" class="form-control h-100" cols="30" rows="10"></textarea>
                                <span asp-validation-for="Review.ReviewText" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">Update</button>
                </form>
            </div>
        </div>
    </div>
</div>
