@model CarPointCMS.Models.Entities.User

<div class="modern-sidebar">
    <!-- User Profile Section -->
    <div class="sidebar-profile">
        <div class="profile-avatar">
            @if (!string.IsNullOrEmpty(Model?.Photo))
            {
                <img src="~/uploads/user_photos/@Model.Photo" alt="@Model.Name" class="profile-image" />
            }
            else
            {
                <i class="fas fa-user-circle"></i>
            }
        </div>
        <div class="profile-info">
            <h5 class="profile-name">@(Model?.Name ?? "Customer")</h5>
            <p class="profile-role">@(Model?.Email ?? "Manage your account")</p>
        </div>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <!-- Dashboard Section -->
        <div class="nav-section">
            <h6 class="nav-section-title">Overview</h6>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="@Url.Action("Dashboard", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <span class="nav-text">Dashboard</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Listings Section -->
        <div class="nav-section">
            <h6 class="nav-section-title">Listings</h6>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="@Url.Action("MyListings", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-list"></i>
                        <span class="nav-text">All Listings</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("AddListing", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-plus-circle"></i>
                        <span class="nav-text">Add Listing</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("Wishlist", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-heart"></i>
                        <span class="nav-text">Wishlist</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("MyReviews", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-star"></i>
                        <span class="nav-text">My Reviews</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Packages Section -->
        <div class="nav-section">
            <h6 class="nav-section-title">Packages</h6>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="@Url.Action("Packages", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-box"></i>
                        <span class="nav-text">Packages</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("PurchaseHistory", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-history"></i>
                        <span class="nav-text">Purchase History</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Account Section -->
        <div class="nav-section">
            <h6 class="nav-section-title">Account Settings</h6>
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="@Url.Action("EditProfile", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-user-edit"></i>
                        <span class="nav-text">Edit Profile</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("EditPassword", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-lock"></i>
                        <span class="nav-text">Change Password</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("EditPhoto", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-camera"></i>
                        <span class="nav-text">Profile Photo</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="@Url.Action("EditBanner", "Customer", new { area = "Customer" })" class="nav-link">
                        <i class="nav-icon fas fa-image"></i>
                        <span class="nav-text">Profile Banner</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Logout Section -->
        <div class="nav-section nav-section-logout">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="@Url.Action("Logout", "Account", new { area = "Customer" })" class="nav-link nav-link-logout">
                        <i class="nav-icon fas fa-sign-out-alt"></i>
                        <span class="nav-text">Logout</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
</div>
